<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话内容生成测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-word;
        }
        .model-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .model-gpt4o { background: #10b981; color: white; }
        .model-gpt4o-mini { background: #3b82f6; color: white; }
        .model-doubao { background: #8b5cf6; color: white; }
        .model-default { background: #6b7280; color: white; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">对话内容生成测试</h1>
            <p class="text-gray-600">测试AI模型生成对话的质量和效果</p>
        </div>

        <!-- 配置面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8 card-hover">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">生成配置</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 模式选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">学习模式</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="mode" value="scene" checked class="mr-2 text-blue-600">
                            <span class="text-sm">场景跟读</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="mode" value="free" class="mr-2 text-blue-600">
                            <span class="text-sm">自由对话</span>
                        </label>
                    </div>
                </div>

                <!-- 模型选择 -->
                <div>
                    <label for="modelSelect" class="block text-sm font-medium text-gray-700 mb-2">AI模型</label>
                    <select id="modelSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="gpt-4o">GPT-4o</option>
                        <option value="gpt-4o-mini" selected>GPT-4o-mini</option>
                        <option value="doubao-1-5-pro-32k-250115">豆包-1.5-Pro</option>
                        <option value="gpt-3.5-turbo">GPT-3.5-turbo</option>
                    </select>
                </div>

                <!-- 难度选择 -->
                <div>
                    <label for="difficultySelect" class="block text-sm font-medium text-gray-700 mb-2">难度等级</label>
                    <select id="difficultySelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="easy">简单 (Easy)</option>
                        <option value="medium">中等 (Medium)</option>
                        <option value="hard">困难 (Hard)</option>
                    </select>
                </div>

                <!-- 话题选择 -->
                <div>
                    <label for="topicSelect" class="block text-sm font-medium text-gray-700 mb-2">预设话题</label>
                    <select id="topicSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择话题...</option>
                    </select>
                </div>

                <!-- 自定义话题 -->
                <div>
                    <label for="customTopic" class="block text-sm font-medium text-gray-700 mb-2">自定义话题</label>
                    <input type="text" id="customTopic" placeholder="输入自定义话题..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- 生成按钮 -->
                <div class="space-y-2">
                    <button id="demoBtn" class="w-full bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        🎭 演示模式
                    </button>
                    <button id="generateBtn" class="w-full bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm">
                        <span id="generateText">真实AI生成</span>
                        <span id="generateSpinner" class="loading hidden inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full ml-2"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 提示词管理区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8 card-hover">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center gap-3">
                    <h2 class="text-xl font-semibold text-gray-800">提示词管理</h2>
                    <span id="modificationStatus" class="text-sm px-2 py-1 bg-gray-100 text-gray-600 rounded hidden">
                        未修改
                    </span>
                </div>
                <div class="flex gap-2">
                    <button id="loadPromptsBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        加载提示词
                    </button>
                    <button id="togglePromptEditor" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        编辑提示词
                    </button>
                </div>
            </div>

            <!-- 当前提示词显示 -->
            <div id="currentPromptDisplay" class="mb-6">
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-lg font-medium text-gray-700">当前使用的提示词</h3>
                    <button id="previewPromptBtn" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        预览提示词
                    </button>
                </div>
                <div id="currentPromptInfo" class="bg-gray-50 rounded-lg p-4 border">
                    <div class="text-sm text-gray-500 mb-2">请先加载提示词或生成对话后查看提示词</div>
                </div>
            </div>

            <!-- 提示词编辑器 -->
            <div id="promptEditor" class="hidden">
                <h3 class="text-lg font-medium text-gray-700 mb-3">编辑提示词</h3>

                <!-- 提示词选择 -->
                <div class="mb-4">
                    <label for="promptSelect" class="block text-sm font-medium text-gray-700 mb-2">选择提示词模板</label>
                    <select id="promptSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择提示词模板...</option>
                    </select>
                </div>

                <!-- 提示词内容编辑 -->
                <div class="mb-4">
                    <label for="promptContent" class="block text-sm font-medium text-gray-700 mb-2">提示词内容</label>
                    <textarea id="promptContent" rows="10" placeholder="请选择提示词模板后编辑..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2">
                    <button id="applyPromptBtn" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        应用修改（仅当前会话）
                    </button>
                    <button id="resetPromptBtn" class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        重置
                    </button>
                    <button id="clearModificationsBtn" class="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        清除所有修改
                    </button>
                </div>
            </div>
        </div>

        <!-- 结果展示区域 -->
        <div id="resultsContainer" class="hidden">
            <!-- 生成信息 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">生成信息</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">模式:</span>
                        <span id="resultMode" class="font-medium ml-1"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">模型:</span>
                        <span id="resultModel" class="font-medium ml-1"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">话题:</span>
                        <span id="resultTopic" class="font-medium ml-1"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">难度:</span>
                        <span id="resultDifficulty" class="font-medium ml-1"></span>
                    </div>
                </div>
            </div>

            <!-- 对话展示 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 card-hover">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">生成的对话</h3>
                    <div class="flex gap-2">
                        <button id="showParsedBtn" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                            解析结果
                        </button>
                        <button id="showRawBtn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                            原始内容
                        </button>
                    </div>
                </div>

                <!-- 解析后的对话 -->
                <div id="parsedDialogue" class="space-y-3">
                    <!-- 对话内容将在这里动态生成 -->
                </div>

                <!-- 原始内容 -->
                <div id="rawContent" class="hidden">
                    <pre class="code-block" id="rawContentText"></pre>
                </div>
            </div>

            <!-- 质量评估 -->
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">质量评估</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">整体质量评分</label>
                        <div class="flex gap-2">
                            <button class="quality-btn px-4 py-2 border rounded-md transition-colors hover:bg-green-50 border-green-300 text-green-600" data-score="5">
                                ⭐⭐⭐⭐⭐ 优秀
                            </button>
                            <button class="quality-btn px-4 py-2 border rounded-md transition-colors hover:bg-blue-50 border-blue-300 text-blue-600" data-score="4">
                                ⭐⭐⭐⭐ 良好
                            </button>
                            <button class="quality-btn px-4 py-2 border rounded-md transition-colors hover:bg-yellow-50 border-yellow-300 text-yellow-600" data-score="3">
                                ⭐⭐⭐ 一般
                            </button>
                            <button class="quality-btn px-4 py-2 border rounded-md transition-colors hover:bg-orange-50 border-orange-300 text-orange-600" data-score="2">
                                ⭐⭐ 较差
                            </button>
                            <button class="quality-btn px-4 py-2 border rounded-md transition-colors hover:bg-red-50 border-red-300 text-red-600" data-score="1">
                                ⭐ 很差
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="qualityNotes" class="block text-sm font-medium text-gray-700 mb-2">评估备注</label>
                        <textarea id="qualityNotes" rows="3" placeholder="请输入对生成内容的评估意见..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <button id="saveEvaluation" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        保存评估
                    </button>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-16">
            <div class="text-gray-400 text-6xl mb-4">🤖</div>
            <h3 class="text-xl font-medium text-gray-600 mb-2">准备生成对话</h3>
            <p class="text-gray-500">配置参数后点击"生成对话"按钮开始测试</p>
        </div>
    </div>

    <script>
        class DialogueGenerator {
            constructor() {
                this.currentResult = null;
                this.currentScore = null;
                this.basePath = this.getBasePath();
                this.prompts = [];
                this.currentPrompt = null;
                this.originalPromptContent = '';
                this.modifiedPrompts = {}; // 存储修改后的提示词（仅在当前会话中生效）
                this.init();
            }

            // 检测当前路径是否包含 /ai 前缀
            getBasePath() {
                const path = window.location.pathname;
                return path.startsWith('/ai') ? '/ai' : '';
            }

            init() {
                this.loadTopics();
                this.bindEvents();
                this.bindPromptEvents();
            }

            async loadTopics() {
                try {
                    // 判断是否为本地环境
                    const url = window.location.href;
                    if (url.includes('http://localhost') || url.includes('http://127.0.0.1')|| url.includes('http://ait.fm210.cn')) {
                        // 本地环境
                        const response = await axios.get('/topics');
                        const topics = response.data;
                        const topicSelect = document.getElementById('topicSelect');

                        topics.forEach(topic => {
                            const option = document.createElement('option');
                            option.value = topic.topic_id;
                            option.textContent = `${topic.title_zh} (${topic.category})`;
                            topicSelect.appendChild(option);
                        });
                    }else{
                        // 线上环境
                        const response = await axios.get('/ai/topics');
                        const topics = response.data;
                        const topicSelect = document.getElementById('topicSelect');

                        topics.forEach(topic => {
                            const option = document.createElement('option');
                            option.value = topic.topic_id;
                            option.textContent = `${topic.title_zh} (${topic.category})`;
                            topicSelect.appendChild(option);
                        });
                    }

                } catch (error) {
                    console.error('加载话题失败:', error);
                    this.showError('加载话题失败，请刷新页面重试');
                }
            }

            bindEvents() {
                // 生成按钮
                document.getElementById('generateBtn').addEventListener('click', () => {
                    this.generateDialogue();
                });

                // 演示按钮
                document.getElementById('demoBtn').addEventListener('click', () => {
                    this.generateDialogue(true);
                });

                // 显示切换按钮
                document.getElementById('showParsedBtn').addEventListener('click', () => {
                    this.showParsedDialogue();
                });

                document.getElementById('showRawBtn').addEventListener('click', () => {
                    this.showRawContent();
                });

                // 质量评分按钮
                document.querySelectorAll('.quality-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.setQualityScore(e.target.dataset.score);
                    });
                });

                // 保存评估
                document.getElementById('saveEvaluation').addEventListener('click', () => {
                    this.saveEvaluation();
                });
            }

            bindPromptEvents() {
                // 加载提示词按钮
                document.getElementById('loadPromptsBtn').addEventListener('click', () => {
                    this.loadPrompts();
                });

                // 预览提示词按钮
                document.getElementById('previewPromptBtn').addEventListener('click', () => {
                    if (this.prompts.length === 0) {
                        this.loadPrompts().then(() => {
                            this.previewCurrentPrompt();
                        });
                    } else {
                        this.previewCurrentPrompt();
                    }
                });

                // 切换提示词编辑器
                document.getElementById('togglePromptEditor').addEventListener('click', () => {
                    this.togglePromptEditor();
                });

                // 提示词选择变化
                document.getElementById('promptSelect').addEventListener('change', (e) => {
                    this.selectPrompt(e.target.value);
                });

                // 应用提示词修改
                document.getElementById('applyPromptBtn').addEventListener('click', () => {
                    this.applyPromptModification();
                });

                // 重置提示词
                document.getElementById('resetPromptBtn').addEventListener('click', () => {
                    this.resetPrompt();
                });

                // 清除所有修改
                document.getElementById('clearModificationsBtn').addEventListener('click', () => {
                    this.clearAllModifications();
                });

                // 监听配置变化以预览提示词
                document.querySelectorAll('input[name="mode"]').forEach(radio => {
                    radio.addEventListener('change', () => {
                        if (this.prompts.length > 0) {
                            this.previewCurrentPrompt();
                        }
                    });
                });

                document.getElementById('topicSelect').addEventListener('change', () => {
                    if (this.prompts.length > 0) {
                        this.previewCurrentPrompt();
                    }
                });

                document.getElementById('customTopic').addEventListener('input', () => {
                    if (this.prompts.length > 0) {
                        this.previewCurrentPrompt();
                    }
                });
            }

            async generateDialogue(isDemo = false) {
                const mode = document.querySelector('input[name="mode"]:checked').value;
                const model = document.getElementById('modelSelect').value;
                const difficulty = document.getElementById('difficultySelect').value;
                const topicId = document.getElementById('topicSelect').value;
                const customTopic = document.getElementById('customTopic').value.trim();

                if (!isDemo && !topicId && !customTopic) {
                    this.showError('请选择话题或输入自定义话题');
                    return;
                }

                this.setLoading(true);

                try {
                    const params = {
                        mode: mode,
                        model: model,
                        difficulty: difficulty
                    };

                    // 添加自定义提示词（如果有的话）
                    const customPrompt = this.getCurrentCustomPrompt(mode, topicId, customTopic);
                    if (customPrompt) {
                        params.custom_prompt = customPrompt;
                    }

                    if (isDemo) {
                        params.demo = true;
                        params.topic_id = topicId || '1'; // 演示模式使用默认话题
                    } else {
                        if (topicId) {
                            params.topic_id = topicId;
                        }
                        if (customTopic) {
                            params.custom_topic = customTopic;
                        }
                    }

                    // 判断是否为本地环境
                    const url = window.location.href;
                    let generateUrl;
                    if (url.includes('http://localhost') || url.includes('http://127.0.0.1') || url.includes('http://ait.fm210.cn')) {
                        generateUrl = '/generateTest';
                    } else {
                        generateUrl = '/ai/generateTest';
                    }

                    const response = await axios.post(generateUrl, params);

                    if (response.data.success) {
                        // 检查是否为异步响应
                        if (response.data.async) {
                            // 处理异步响应 - 使用WebSocket
                            await this.handleAsyncResponse(response.data);
                        } else if (response.data.streaming) {
                            // 处理流式响应（旧版本兼容）
                            await this.handleStreamingResponse(response.data.data);
                        } else {
                            // 处理普通响应（演示模式）
                            this.currentResult = response.data.data;
                            this.displayResult();
                            this.hideEmptyState();
                        }
                    } else {
                        this.showError(response.data.error || '生成失败');
                    }
                } catch (error) {
                    console.error('生成对话失败:', error);
                    const errorData = error.response?.data;
                    let errorMsg = errorData?.error || '生成对话失败，请重试';

                    if (errorData?.suggestion) {
                        errorMsg += '\n\n' + errorData.suggestion;
                    }

                    this.showError(errorMsg);
                } finally {
                    this.setLoading(false);
                }
            }

            async handleAsyncResponse(asyncData) {
                try {
                    const taskId = asyncData.task_id;
                    const websocketUrl = asyncData.websocket_url;

                    // 显示异步处理状态
                    this.showAsyncStatus(taskId);

                    // 连接WebSocket
                    const ws = new WebSocket(websocketUrl);

                    ws.onopen = () => {
                        console.log('WebSocket连接成功');
                        // 订阅任务
                        ws.send(JSON.stringify({
                            type: 'subscribe',
                            task_id: taskId
                        }));
                        this.updateAsyncStatus('已连接WebSocket，等待处理结果...');
                    };

                    ws.onmessage = (event) => {
                        try {
                            const message = JSON.parse(event.data);
                            console.log('收到WebSocket消息:', message);

                            switch(message.type) {
                                case 'subscribed':
                                    this.updateAsyncStatus('已订阅任务，正在处理...');
                                    break;

                                case 'progress':
                                    this.updateAsyncProgress(message.data.progress, message.data.message);
                                    break;

                                case 'result':
                                    if (message.data && message.data.success && message.data.data) {
                                        this.currentResult = message.data.data;
                                        this.displayResult();
                                        this.hideEmptyState();
                                        this.hideAsyncStatus();
                                        ws.close();
                                    } else {
                                        this.showError('生成结果格式错误');
                                        this.hideAsyncStatus();
                                        ws.close();
                                    }
                                    break;

                                case 'error':
                                    this.showError(message.data.error || '任务处理失败');
                                    this.hideAsyncStatus();
                                    ws.close();
                                    break;
                            }
                        } catch (e) {
                            console.error('解析WebSocket消息失败:', e);
                        }
                    };

                    ws.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.showError('WebSocket连接失败，请检查网络连接');
                        this.hideAsyncStatus();
                    };

                    ws.onclose = () => {
                        console.log('WebSocket连接关闭');
                    };

                } catch (error) {
                    console.error('异步处理失败:', error);
                    this.hideAsyncStatus();
                    throw error;
                }
            }

            async handleStreamingResponse(streamingParams) {
                try {
                    // 显示流式状态
                    this.showStreamingStatus();

                    // 使用 fetch 处理流式响应
                    const response = await fetch(streamingParams.url, {
                        method: 'POST',
                        headers: streamingParams.headers,
                        body: JSON.stringify(streamingParams.body)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let content = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data === '[DONE]') {
                                    break;
                                }
                                try {
                                    const parsed = JSON.parse(data);
                                    const delta = parsed.choices?.[0]?.delta?.content;
                                    if (delta) {
                                        content += delta;
                                        // 实时更新显示
                                        this.updateStreamingContent(content);
                                    }
                                } catch (e) {
                                    // 忽略解析错误
                                }
                            }
                        }
                    }

                    // 流式完成，处理最终结果
                    console.log('Final streaming content:', content); // 调试日志
                    const dialogues = this.parseGeneratedDialogue(content);
                    this.currentResult = {
                        raw_content: content,
                        dialogues: dialogues,
                        mode: streamingParams.metadata.mode,
                        model: streamingParams.body.model,
                        topic: streamingParams.metadata.topic,
                        difficulty: streamingParams.metadata.difficulty,
                        prompt_module: streamingParams.metadata.prompt_module,
                        prompt_info: streamingParams.metadata.prompt_info
                    };

                    // 更新当前提示词显示
                    if (streamingParams.metadata.prompt_info) {
                        this.updateCurrentPromptDisplay(streamingParams.metadata.prompt_info);
                    }

                    this.displayResult();
                    this.hideEmptyState();
                    this.hideStreamingStatus();

                } catch (error) {
                    console.error('流式处理失败:', error);
                    this.hideStreamingStatus();
                    throw error;
                }
            }

            parseGeneratedDialogue(content) {
                const dialogues = [];
                console.log('Parsing content:', content); // 调试日志

                // 先尝试解析为完整的JSON数组
                try {
                    const jsonData = JSON.parse(content);
                    if (Array.isArray(jsonData)) {
                        jsonData.forEach((item, index) => {
                            let role = 'user'; // 默认为用户

                            // 根据AI返回的角色名称进行映射
                            if (item.role) {
                                const roleStr = item.role.toLowerCase();
                                if (roleStr.includes('导游') || roleStr.includes('系统') || roleStr.includes('system') ||
                                    roleStr.includes('服务员') || roleStr.includes('老师') || roleStr.includes('工作人员') ||
                                    roleStr.includes('商家') || roleStr.includes('客服')) {
                                    role = 'system';
                                } else if (roleStr.includes('游客') || roleStr.includes('用户') || roleStr.includes('user') ||
                                          roleStr.includes('客人') || roleStr.includes('学生') || roleStr.includes('顾客')) {
                                    role = 'user';
                                }
                            }

                            dialogues.push({
                                sort_order: index,
                                role: role,
                                content_zh: item.dialogue || item.content || item.text || '',
                                content_vi: '' // 暂时不翻译
                            });
                        });

                        console.log('Parsed as JSON array:', dialogues);
                        return dialogues;
                    }
                } catch (e) {
                    console.log('Not a valid JSON array, trying other formats...');
                }

                // 尝试解析单个JSON对象格式
                const regex = /\{\s*["']role["']:\s*["'](.*?)["'],\s*["']dialogue["']:\s*["'](.*?)["']\s*\}/gs;
                let match;

                while ((match = regex.exec(content)) !== null) {
                    let role = 'user'; // 默认为用户
                    const roleStr = match[1].toLowerCase();

                    // 根据角色名称进行映射
                    if (roleStr.includes('导游') || roleStr.includes('系统') || roleStr.includes('system') ||
                        roleStr.includes('服务员') || roleStr.includes('老师') || roleStr.includes('工作人员') ||
                        roleStr.includes('商家') || roleStr.includes('客服')) {
                        role = 'system';
                    } else if (roleStr.includes('游客') || roleStr.includes('用户') || roleStr.includes('user') ||
                              roleStr.includes('客人') || roleStr.includes('学生') || roleStr.includes('顾客')) {
                        role = 'user';
                    }

                    dialogues.push({
                        sort_order: dialogues.length,
                        role: role,
                        content_zh: match[2],
                        content_vi: '' // 暂时不翻译
                    });
                }

                // 如果JSON解析失败，尝试其他格式
                if (dialogues.length === 0) {
                    // 尝试解析类似 "A: 内容" 或 "B: 内容" 的格式
                    const roleLines = content.split('\n').filter(line => line.trim());

                    for (let i = 0; i < roleLines.length; i++) {
                        const line = roleLines[i].trim();
                        if (line) {
                            // 检查是否有角色标识
                            const roleMatch = line.match(/^([AB系统用户])[:]：?\s*(.+)$/);
                            if (roleMatch) {
                                const roleIndicator = roleMatch[1];
                                const content_zh = roleMatch[2];
                                let role = 'user';

                                // 根据标识符判断角色
                                if (roleIndicator === 'A' || roleIndicator === '系统') {
                                    role = 'system';
                                } else if (roleIndicator === 'B' || roleIndicator === '用户') {
                                    role = 'user';
                                }

                                dialogues.push({
                                    sort_order: dialogues.length,
                                    role: role,
                                    content_zh: content_zh,
                                    content_vi: ''
                                });
                            } else {
                                // 没有角色标识，使用简单的交替规则
                                const currentIndex = dialogues.length;
                                const role = (currentIndex % 2 === 0) ? 'system' : 'user';
                                console.log(`Line ${currentIndex}: "${line}" -> role: ${role}`); // 调试日志
                                dialogues.push({
                                    sort_order: currentIndex,
                                    role: role,
                                    content_zh: line,
                                    content_vi: ''
                                });
                            }
                        }
                    }
                }

                // 如果已经解析到内容，直接返回
                if (dialogues.length > 0) {
                    console.log('Final parsed dialogues:', dialogues); // 调试日志
                    return dialogues;
                }

                console.log('No dialogues parsed, content might be in unexpected format'); // 调试日志

                // 临时调试：在页面上显示原始内容
                if (typeof window !== 'undefined' && window.location.search.includes('debug=1')) {
                    const debugDiv = document.createElement('div');
                    debugDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: black; color: white; padding: 10px; max-width: 300px; z-index: 9999; font-size: 12px; white-space: pre-wrap;';
                    debugDiv.textContent = 'Raw content:\n' + content;
                    document.body.appendChild(debugDiv);
                    setTimeout(() => debugDiv.remove(), 10000);
                }

                return dialogues;
            }

            showAsyncStatus(taskId) {
                // 显示异步处理状态提示
                const container = document.getElementById('resultsContainer');
                container.classList.remove('hidden');

                const asyncDiv = document.createElement('div');
                asyncDiv.id = 'asyncStatus';
                asyncDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6';
                asyncDiv.innerHTML = `
                    <div class="flex items-center mb-3">
                        <div class="loading inline-block w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full mr-3"></div>
                        <span class="text-blue-800 font-medium">异步处理中...</span>
                    </div>
                    <div class="text-sm text-blue-600 mb-2">任务ID: <span class="font-mono">${taskId}</span></div>
                    <div id="asyncStatusText" class="text-sm text-blue-700">正在连接WebSocket...</div>
                    <div id="asyncProgress" class="mt-3 hidden">
                        <div class="flex justify-between text-sm text-blue-700 mb-1">
                            <span id="asyncProgressText">处理中...</span>
                            <span id="asyncProgressPercent">0%</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div id="asyncProgressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                `;

                container.insertBefore(asyncDiv, container.firstChild);
                this.hideEmptyState();
            }

            updateAsyncStatus(message) {
                const statusText = document.getElementById('asyncStatusText');
                if (statusText) {
                    statusText.textContent = message;
                }
            }

            updateAsyncProgress(progress, message) {
                const progressContainer = document.getElementById('asyncProgress');
                const progressBar = document.getElementById('asyncProgressBar');
                const progressText = document.getElementById('asyncProgressText');
                const progressPercent = document.getElementById('asyncProgressPercent');

                if (progressContainer && progressBar && progressText && progressPercent) {
                    progressContainer.classList.remove('hidden');
                    progressBar.style.width = progress + '%';
                    progressText.textContent = message;
                    progressPercent.textContent = progress + '%';
                }
            }

            hideAsyncStatus() {
                const asyncDiv = document.getElementById('asyncStatus');
                if (asyncDiv) {
                    asyncDiv.remove();
                }
            }

            showStreamingStatus() {
                // 显示流式状态提示
                const container = document.getElementById('resultsContainer');
                container.classList.remove('hidden');

                const streamingDiv = document.createElement('div');
                streamingDiv.id = 'streamingStatus';
                streamingDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6';
                streamingDiv.innerHTML = `
                    <div class="flex items-center">
                        <div class="loading inline-block w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full mr-3"></div>
                        <span class="text-blue-800 font-medium">正在生成对话内容...</span>
                    </div>
                    <div id="streamingContent" class="mt-3 p-3 bg-white rounded border text-sm font-mono whitespace-pre-wrap"></div>
                `;

                container.insertBefore(streamingDiv, container.firstChild);
                this.hideEmptyState();
            }

            updateStreamingContent(content) {
                const contentDiv = document.getElementById('streamingContent');
                if (contentDiv) {
                    contentDiv.textContent = content;
                    contentDiv.scrollTop = contentDiv.scrollHeight;
                }
            }

            hideStreamingStatus() {
                const streamingDiv = document.getElementById('streamingStatus');
                if (streamingDiv) {
                    streamingDiv.remove();
                }
            }

            displayResult() {
                const result = this.currentResult;

                // 显示生成信息
                document.getElementById('resultMode').textContent = result.mode === 'scene' ? '场景跟读' : '自由对话';
                document.getElementById('resultModel').innerHTML = this.getModelBadge(result.model);
                document.getElementById('resultTopic').textContent = result.topic;
                document.getElementById('resultDifficulty').textContent = result.difficulty;

                // 显示对话内容
                this.renderDialogues(result.dialogues);
                document.getElementById('rawContentText').textContent = result.raw_content;

                // 显示结果容器
                document.getElementById('resultsContainer').classList.remove('hidden');
                document.getElementById('resultsContainer').classList.add('fade-in');

                // 默认显示解析结果
                this.showParsedDialogue();

                // 重置评估
                this.resetEvaluation();
            }

            renderDialogues(dialogues) {
                const container = document.getElementById('parsedDialogue');
                container.innerHTML = '';

                console.log('Rendering dialogues:', dialogues); // 调试日志

                if (!dialogues || dialogues.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">未能解析出对话内容</p>';
                    return;
                }

                dialogues.forEach((dialogue, index) => {
                    console.log(`Dialogue ${index}:`, dialogue); // 调试日志
                    const dialogueDiv = document.createElement('div');
                    dialogueDiv.className = `p-4 rounded-lg ${dialogue.role === 'system' ? 'bg-blue-50 border-l-4 border-blue-400' : 'bg-green-50 border-l-4 border-green-400'}`;

                    dialogueDiv.innerHTML = `
                        <div class="flex items-center mb-2">
                            <span class="text-xs font-semibold px-2 py-1 rounded ${dialogue.role === 'system' ? 'bg-blue-200 text-blue-800' : 'bg-green-200 text-green-800'}">
                                ${dialogue.role === 'system' ? '系统' : '用户'}
                            </span>
                            <span class="text-xs text-gray-500 ml-2">第 ${index + 1} 句</span>
                        </div>
                        <div class="text-gray-800 font-medium">${dialogue.content_zh}</div>
                        ${dialogue.content_vi ? `<div class="text-gray-600 text-sm mt-1">${dialogue.content_vi}</div>` : ''}
                    `;

                    container.appendChild(dialogueDiv);
                });
            }

            getModelBadge(model) {
                let className = 'model-default';
                if (model.includes('gpt-4o-mini')) className = 'model-gpt4o-mini';
                else if (model.includes('gpt-4o')) className = 'model-gpt4o';
                else if (model.includes('doubao')) className = 'model-doubao';

                return `<span class="model-badge ${className}">${model}</span>`;
            }

            showParsedDialogue() {
                document.getElementById('parsedDialogue').classList.remove('hidden');
                document.getElementById('rawContent').classList.add('hidden');

                document.getElementById('showParsedBtn').classList.add('bg-blue-100', 'text-blue-700');
                document.getElementById('showParsedBtn').classList.remove('bg-gray-100', 'text-gray-700');

                document.getElementById('showRawBtn').classList.add('bg-gray-100', 'text-gray-700');
                document.getElementById('showRawBtn').classList.remove('bg-blue-100', 'text-blue-700');
            }

            showRawContent() {
                document.getElementById('parsedDialogue').classList.add('hidden');
                document.getElementById('rawContent').classList.remove('hidden');

                document.getElementById('showRawBtn').classList.add('bg-blue-100', 'text-blue-700');
                document.getElementById('showRawBtn').classList.remove('bg-gray-100', 'text-gray-700');

                document.getElementById('showParsedBtn').classList.add('bg-gray-100', 'text-gray-700');
                document.getElementById('showParsedBtn').classList.remove('bg-blue-100', 'text-blue-700');
            }

            setQualityScore(score) {
                this.currentScore = score;

                // 重置所有按钮样式
                document.querySelectorAll('.quality-btn').forEach(btn => {
                    btn.classList.remove('bg-green-500', 'bg-blue-500', 'bg-yellow-500', 'bg-orange-500', 'bg-red-500', 'text-white');
                    btn.classList.add('border');
                });

                // 设置选中按钮样式
                const selectedBtn = document.querySelector(`[data-score="${score}"]`);
                if (selectedBtn) {
                    selectedBtn.classList.remove('border');
                    selectedBtn.classList.add('text-white');

                    if (score >= 4) selectedBtn.classList.add('bg-green-500');
                    else if (score >= 3) selectedBtn.classList.add('bg-blue-500');
                    else if (score >= 2) selectedBtn.classList.add('bg-yellow-500');
                    else selectedBtn.classList.add('bg-red-500');
                }
            }

            resetEvaluation() {
                this.currentScore = null;
                document.getElementById('qualityNotes').value = '';

                // 重置所有评分按钮
                document.querySelectorAll('.quality-btn').forEach(btn => {
                    btn.classList.remove('bg-green-500', 'bg-blue-500', 'bg-yellow-500', 'bg-orange-500', 'bg-red-500', 'text-white');
                    btn.classList.add('border');
                });
            }

            saveEvaluation() {
                if (!this.currentScore) {
                    this.showError('请先选择质量评分');
                    return;
                }

                const notes = document.getElementById('qualityNotes').value.trim();

                // 这里可以发送评估数据到后端保存
                const evaluation = {
                    result: this.currentResult,
                    score: this.currentScore,
                    notes: notes,
                    timestamp: new Date().toISOString()
                };

                console.log('保存评估:', evaluation);

                // 显示成功消息
                this.showSuccess('评估已保存');
            }

            setLoading(loading) {
                const button = document.getElementById('generateBtn');
                const text = document.getElementById('generateText');
                const spinner = document.getElementById('generateSpinner');

                if (loading) {
                    button.disabled = true;
                    text.textContent = '生成中...';
                    spinner.classList.remove('hidden');
                } else {
                    button.disabled = false;
                    text.textContent = '生成对话';
                    spinner.classList.add('hidden');
                }
            }

            hideEmptyState() {
                document.getElementById('emptyState').style.display = 'none';
            }

            // 提示词管理方法
            async loadPrompts() {
                try {
                    const url = window.location.href;
                    let promptsUrl;
                    if (url.includes('http://localhost') || url.includes('http://127.0.0.1') || url.includes('http://ait.fm210.cn')) {
                        promptsUrl = '/getPrompts';
                    } else {
                        promptsUrl = '/ai/getPrompts';
                    }

                    const response = await axios.get(promptsUrl);
                    this.prompts = response.data;

                    // 更新提示词选择器
                    const promptSelect = document.getElementById('promptSelect');
                    promptSelect.innerHTML = '<option value="">请选择提示词模板...</option>';

                    this.prompts.forEach(prompt => {
                        const option = document.createElement('option');
                        option.value = prompt.prompt_id;
                        option.textContent = `${prompt.desc} (${prompt.module})`;
                        promptSelect.appendChild(option);
                    });

                    this.showSuccess('提示词加载成功');
                } catch (error) {
                    console.error('加载提示词失败:', error);
                    this.showError('加载提示词失败，请重试');
                }
            }

            togglePromptEditor() {
                const editor = document.getElementById('promptEditor');
                const button = document.getElementById('togglePromptEditor');

                if (editor.classList.contains('hidden')) {
                    editor.classList.remove('hidden');
                    button.textContent = '隐藏编辑器';
                    // 如果还没有加载提示词，自动加载
                    if (this.prompts.length === 0) {
                        this.loadPrompts();
                    }
                } else {
                    editor.classList.add('hidden');
                    button.textContent = '编辑提示词';
                }
            }

            selectPrompt(promptId) {
                if (!promptId) {
                    document.getElementById('promptContent').value = '';
                    this.currentPrompt = null;
                    this.originalPromptContent = '';
                    return;
                }

                const prompt = this.prompts.find(p => p.prompt_id == promptId);
                if (prompt) {
                    this.currentPrompt = prompt;
                    this.originalPromptContent = prompt.prompt_template;

                    // 检查是否有修改后的版本
                    const modifiedContent = this.modifiedPrompts[prompt.module];
                    document.getElementById('promptContent').value = modifiedContent || prompt.prompt_template;
                }
            }

            applyPromptModification() {
                if (!this.currentPrompt) {
                    this.showError('请先选择提示词模板');
                    return;
                }

                const newContent = document.getElementById('promptContent').value.trim();
                if (!newContent) {
                    this.showError('提示词内容不能为空');
                    return;
                }

                // 在当前会话中保存修改后的提示词
                this.modifiedPrompts[this.currentPrompt.module] = newContent;

                // 更新当前显示
                this.updateCurrentPromptDisplay({
                    ...this.currentPrompt,
                    prompt_template: newContent,
                    desc: this.currentPrompt.desc + ' (已修改)'
                });

                this.showSuccess('提示词修改已应用（仅在当前会话中生效）');
                this.updateModificationStatus();
            }

            clearAllModifications() {
                this.modifiedPrompts = {};
                this.showSuccess('所有提示词修改已清除');

                // 如果当前有选中的提示词，重新显示原始内容
                if (this.currentPrompt) {
                    this.updateCurrentPromptDisplay(this.currentPrompt);
                    document.getElementById('promptContent').value = this.currentPrompt.prompt_template;
                }
                this.updateModificationStatus();
            }

            updateModificationStatus() {
                const statusElement = document.getElementById('modificationStatus');
                const modifiedCount = Object.keys(this.modifiedPrompts).length;

                if (modifiedCount > 0) {
                    statusElement.textContent = `已修改 ${modifiedCount} 个提示词`;
                    statusElement.className = 'text-sm px-2 py-1 bg-orange-100 text-orange-600 rounded';
                    statusElement.classList.remove('hidden');
                } else {
                    statusElement.textContent = '未修改';
                    statusElement.className = 'text-sm px-2 py-1 bg-gray-100 text-gray-600 rounded hidden';
                }
            }

            getCurrentCustomPrompt(mode, topicId, customTopic) {
                // 获取选中的话题文本
                let topicText = '导游中文'; // 默认值
                if (customTopic) {
                    topicText = customTopic;
                } else if (topicId) {
                    const topicSelect = document.getElementById('topicSelect');
                    const selectedOption = topicSelect.options[topicSelect.selectedIndex];
                    if (selectedOption && selectedOption.textContent) {
                        const match = selectedOption.textContent.match(/\(([^)]+)\)$/);
                        if (match) {
                            topicText = match[1];
                        }
                    }
                }

                // 提示词映射
                const promptMap = {
                    '导游中文': 'dialogue_generation_dyzw',
                    '工厂中文': 'dialogue_generation_gczw',
                    '商务中文': 'dialogue_generation_swzw',
                    '电商中文': 'dialogue_generation_dszw',
                    '中国求学': 'dialogue_generation_zgqx',
                    '时下热点': 'dialogue_generation_sxrd',
                };

                const promptModule = mode === 'scene'
                    ? (promptMap[topicText] || 'dialogue_generation_dyzw')
                    : 'free_dialogue_generation_first';

                return this.modifiedPrompts[promptModule] || null;
            }

            resetPrompt() {
                if (this.currentPrompt && this.originalPromptContent) {
                    document.getElementById('promptContent').value = this.originalPromptContent;
                } else {
                    document.getElementById('promptContent').value = '';
                }
            }

            updateCurrentPromptDisplay(promptInfo) {
                const container = document.getElementById('currentPromptInfo');
                if (!promptInfo) {
                    container.innerHTML = '<div class="text-sm text-gray-500 mb-2">请先选择配置参数或生成对话后查看提示词</div>';
                    return;
                }

                container.innerHTML = `
                    <div class="mb-3">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="text-sm font-medium text-gray-700">模块:</span>
                            <span class="text-sm text-blue-600 font-mono">${promptInfo.module}</span>
                        </div>
                        <div class="flex items-center gap-2 mb-2">
                            <span class="text-sm font-medium text-gray-700">描述:</span>
                            <span class="text-sm text-gray-600">${promptInfo.desc}</span>
                        </div>
                    </div>
                    <div class="mb-2">
                        <span class="text-sm font-medium text-gray-700">提示词内容:</span>
                    </div>
                    <div class="bg-gray-800 text-gray-100 p-3 rounded font-mono text-xs whitespace-pre-wrap max-h-40 overflow-y-auto">${promptInfo.prompt_template}</div>
                `;
            }

            // 根据当前配置预览提示词
            previewCurrentPrompt() {
                const mode = document.querySelector('input[name="mode"]:checked').value;
                const topicId = document.getElementById('topicSelect').value;
                const customTopic = document.getElementById('customTopic').value.trim();

                // 获取选中的话题文本
                let topicText = '导游中文'; // 默认值
                if (customTopic) {
                    topicText = customTopic;
                } else if (topicId) {
                    const topicSelect = document.getElementById('topicSelect');
                    const selectedOption = topicSelect.options[topicSelect.selectedIndex];
                    if (selectedOption && selectedOption.textContent) {
                        // 从选项文本中提取类别，格式为 "title (category)"
                        const match = selectedOption.textContent.match(/\(([^)]+)\)$/);
                        if (match) {
                            topicText = match[1];
                        }
                    }
                }

                // 提示词映射
                const promptMap = {
                    '导游中文': 'dialogue_generation_dyzw',
                    '工厂中文': 'dialogue_generation_gczw',
                    '商务中文': 'dialogue_generation_swzw',
                    '电商中文': 'dialogue_generation_dszw',
                    '中国求学': 'dialogue_generation_zgqx',
                    '时下热点': 'dialogue_generation_sxrd',
                };

                const promptModule = mode === 'scene'
                    ? (promptMap[topicText] || 'dialogue_generation_dyzw')
                    : 'free_dialogue_generation_first';

                const prompt = this.prompts.find(p => p.module === promptModule);
                if (prompt) {
                    // 检查是否有修改后的版本
                    const modifiedContent = this.modifiedPrompts[promptModule];
                    if (modifiedContent) {
                        this.updateCurrentPromptDisplay({
                            ...prompt,
                            prompt_template: modifiedContent,
                            desc: prompt.desc + ' (已修改)'
                        });
                    } else {
                        this.updateCurrentPromptDisplay(prompt);
                    }
                } else {
                    const container = document.getElementById('currentPromptInfo');
                    container.innerHTML = `<div class="text-sm text-orange-600">未找到对应的提示词模板: ${promptModule}</div>`;
                }
            }

            showError(message) {
                this.showNotification(message, 'error');
            }

            showSuccess(message) {
                this.showNotification(message, 'success');
            }

            showNotification(message, type = 'info') {
                // 创建通知容器（如果不存在）
                let container = document.getElementById('notification-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'notification-container';
                    container.className = 'fixed top-4 right-4 z-50 space-y-2';
                    document.body.appendChild(container);
                }

                // 创建通知元素
                const notification = document.createElement('div');
                const notificationId = 'notification-' + Date.now();
                notification.id = notificationId;

                // 根据类型设置样式
                let bgColor, borderColor, iconColor, icon;
                switch (type) {
                    case 'success':
                        bgColor = 'bg-green-50';
                        borderColor = 'border-green-200';
                        iconColor = 'text-green-600';
                        icon = '✓';
                        break;
                    case 'error':
                        bgColor = 'bg-red-50';
                        borderColor = 'border-red-200';
                        iconColor = 'text-red-600';
                        icon = '✕';
                        break;
                    case 'warning':
                        bgColor = 'bg-yellow-50';
                        borderColor = 'border-yellow-200';
                        iconColor = 'text-yellow-600';
                        icon = '⚠';
                        break;
                    default:
                        bgColor = 'bg-blue-50';
                        borderColor = 'border-blue-200';
                        iconColor = 'text-blue-600';
                        icon = 'ℹ';
                }

                notification.className = `${bgColor} ${borderColor} border rounded-lg p-4 shadow-lg max-w-sm transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;

                notification.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <span class="${iconColor} text-lg font-bold">${icon}</span>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium text-gray-900">${message}</p>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                <span class="sr-only">关闭</span>
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(notification);

                // 触发动画
                setTimeout(() => {
                    notification.classList.remove('translate-x-full', 'opacity-0');
                    notification.classList.add('translate-x-0', 'opacity-100');
                }, 10);

                // 自动移除通知
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.classList.add('translate-x-full', 'opacity-0');
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 300);
                    }
                }, 5000);
            }
        }

        // 初始化应用
        const generator = new DialogueGenerator();
    </script>
</body>
</html>
