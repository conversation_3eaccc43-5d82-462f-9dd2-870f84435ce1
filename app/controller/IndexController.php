<?php

namespace app\controller;

use app\model\User;
use support\Request;
use Tinywan\Jwt\JwtToken;
use app\service\ait\Dialogue;
use app\model\LearningTopics;
use Webman\RedisQueue\Redis as QueRedis;
use app\model\PresetSceneTemplates;
use app\model\PresetSceneDialogues;
use app\model\AiPromptTemplates;
class IndexController
{
    protected $noNeedLogin = [
        'cs','preset','random','check','topics','quality','generator','generateTest','getPrompts','home'
    ];
    public function index(Request $request)
    {
        $user = User::first();
        $token = JwtToken::generateToken([
            "id" => $user->user_id
        ]);
        return toJson(200, 'success', [
            'token' => $token,
            'user' => $user
        ]);
    }

    public function cs(){
        // $dialogue = (new Dialogue())->scene(413);
        $res = Dialogue::preset(2,'easy');
        return json($res);
    }

    # 预设对话
    public function preset(Request $request)
    {
        $difficulty =['easy','medium','hard'];
        # 遍历话题
        foreach (LearningTopics::all() as $topic) {
            # 遍历难度
            foreach ($difficulty as $d) {
                $data = [
                    'topic_id' => $topic->topic_id,
                    'difficulty' => $d,
                ];
                // 队列名
                $queue = 'preset_dialogues';
                // 投递消息
                // Client::send($queue, $data);
                QueRedis::send($queue, $data);
            }
        }
        # 分组统计预设 根据 topic_id 和 difficulty
        $group = PresetSceneTemplates::selectRaw('topic_id, difficulty, count(*) as count')
            ->groupBy('topic_id', 'difficulty')
            ->get();
        return json($group);
    }

    # 随机取指定预设
    public function random(Request $request)
    {
        $topic_id = $request->input('topic_id');
        $difficulty = $request->input('difficulty');

        # 取10条模板
        $templates = PresetSceneTemplates::where('topic_id', $topic_id)
            ->where('difficulty', $difficulty)
            ->inRandomOrder()
            ->limit(10)
            ->get();

        # 获取所有模板ID
        $template_ids = $templates->pluck('template_id')->toArray();

        # 一次性查询所有对话内容
        $dialogues = PresetSceneDialogues::whereIn('template_id', $template_ids)
            ->orderBy('template_id')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('template_id');

        # 组装数据
        $data = $templates->map(function ($item) use ($dialogues) {
            $template_dialogues = $dialogues[$item->template_id] ?? collect();
            return [
                'template_id' => $item->template_id,
                'topic_id' => $item->topic_id,
                'difficulty' => $item->difficulty,
                'dialogues' => $template_dialogues
            ];
        });

        return json($data);
    }
    # 遍历预设，判断预设句子长度是否正确，不正确的删除
    public function check(Request $request)
    {
        # 进度获取
        $id = $request->get('id',0);
        # 获取大于id的100个
        $templates = PresetSceneTemplates::where('template_id', '>', $id)->limit(10000)->get();
        foreach ($templates as $template) {
            # 根据难度判断需要的句子长度
            $Tcount = 7;
            if ($template->difficulty == 'medium') {
                $Tcount = 9;
            } else if ($template->difficulty == 'hard') {
                $Tcount = 11;
            }
            # 判断句子长度
            $dialogues = $template->dialogues;
            $count = count($dialogues);
            if ($count == 0 ) {
                echo "删除模板ID：{$template->template_id} - $count\n";
                PresetSceneDialogues::where('template_id', $template->template_id)->delete();
                PresetSceneTemplates::where('template_id', $template->template_id)->delete();
            }

        }
    }

    # 获取话题列表
    public function topics(Request $request)
    {
        $topics = LearningTopics::where('topic_type', 'preset')
            ->select('topic_id', 'title_zh', 'category')
            ->get();
        return json($topics);
    }

    # 首页
    public function home(Request $request)
    {
        return view('index');
    }

    # 对话质量检查页面
    public function quality(Request $request)
    {
        return view('dialogue/quality');
    }

    # 内容生成测试页面
    public function generator(Request $request)
    {
        return view('dialogue/generator');
    }

    # 获取提示词列表
    public function getPrompts(Request $request)
    {
        $prompts = AiPromptTemplates::select('prompt_id', 'module', 'desc', 'prompt_template')
            ->whereIn('module', [
                'dialogue_generation_dyzw',
                'dialogue_generation_gczw',
                'dialogue_generation_swzw',
                'dialogue_generation_dszw',
                'dialogue_generation_zgqx',
                'dialogue_generation_sxrd',
                'free_dialogue_generation_first',
                'free_dialogue_generation_last'
            ])
            ->get();
        return json($prompts);
    }

    # 测试生成对话 - 异步处理版本
    public function generateTest(Request $request)
    {
        $mode = $request->input('mode'); // 'scene' 或 'free'
        $model = $request->input('model', 'gpt-4o-mini');
        $topic_id = $request->input('topic_id');
        $difficulty = $request->input('difficulty', 'easy');
        $custom_topic = $request->input('custom_topic', '');
        $demo = $request->input('demo', false); // 演示模式
        $custom_prompt = $request->input('custom_prompt', ''); // 自定义提示词

        // 验证参数
        if (!in_array($mode, ['scene', 'free'])) {
            return json(['error' => '无效的模式参数'], 400);
        }

        if (!in_array($difficulty, ['easy', 'medium', 'hard'])) {
            return json(['error' => '无效的难度参数'], 400);
        }

        // 获取话题信息
        $topic = null;
        if ($topic_id) {
            $topic = LearningTopics::find($topic_id);
            if (!$topic) {
                return json(['error' => '话题不存在'], 400);
            }
        }

        // 确定使用的话题文本
        $topicText = $custom_topic ?: ($topic ? $topic->category : '导游中文');

        // 获取提示词
        $promptMap = [
            '导游中文' => 'dialogue_generation_dyzw',
            '工厂中文' => 'dialogue_generation_gczw',
            '商务中文' => 'dialogue_generation_swzw',
            '电商中文' => 'dialogue_generation_dszw',
            '中国求学' => 'dialogue_generation_zgqx',
            '时下热点' => 'dialogue_generation_sxrd',
        ];

        $promptModule = $mode === 'scene'
            ? ($promptMap[$topicText] ?? 'dialogue_generation_dyzw')
            : 'free_dialogue_generation_first';

        // 优先使用自定义提示词，否则使用数据库中的提示词
        if ($custom_prompt) {
            $promptTemplate = $custom_prompt;
            $promptRecord = (object)[
                'prompt_id' => 0,
                'module' => $promptModule . '_custom',
                'desc' => '自定义提示词',
                'prompt_template' => $custom_prompt
            ];
        } else {
            $promptRecord = AiPromptTemplates::where('module', $promptModule)->first();

            if (!$promptRecord) {
                return json(['error' => '提示词模板不存在'], 400);
            }

            $promptTemplate = $promptRecord->prompt_template;
        }

        // 构建用户内容
        $difficultyMap = [
            'easy' => '简单',
            'medium' => '中等',
            'hard' => '困难'
        ];

        $userContent = "话题：{$topicText}\n难度：{$difficultyMap[$difficulty]}";

        try {
            // 非演示模式时检查AI配置
            if (!$demo && (!env('AI_API_KEY') || !env('AI_API'))) {
                return json([
                    'error' => 'AI服务未配置，请使用演示模式',
                    'suggestion' => '请点击"🎭 演示模式"按钮来测试功能'
                ], 400);
            }

            // 生成唯一任务ID
            $taskId = 'generate_test_' . uniqid() . '_' . time();

            // 构建任务数据
            $taskData = [
                'task_id' => $taskId,
                'mode' => $mode,
                'model' => $model,
                'topic_text' => $topicText,
                'difficulty' => $difficulty,
                'prompt_template' => $promptTemplate,
                'user_content' => $userContent,
                'demo' => $demo,
                'prompt_info' => [
                    'prompt_id' => $promptRecord->prompt_id,
                    'module' => $promptRecord->module,
                    'desc' => $promptRecord->desc,
                    'prompt_template' => $promptRecord->prompt_template
                ],
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 投递到队列
            QueRedis::send('generate_test_task', $taskData);

            // 立即返回任务ID和WebSocket连接信息
            return json([
                'success' => true,
                'async' => true,
                'task_id' => $taskId,
                'websocket_url' => 'ws://localhost:8080',
                'message' => '任务已提交，请通过WebSocket连接获取实时进度和结果',
                'instructions' => [
                    '1. 连接到WebSocket: ws://localhost:8080',
                    '2. 发送订阅消息: {"type":"subscribe","task_id":"' . $taskId . '"}',
                    '3. 监听进度消息: type="progress"',
                    '4. 监听结果消息: type="result"',
                    '5. 监听错误消息: type="error"'
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'error' => '任务提交失败：' . $e->getMessage()
            ], 500);
        }
    }

    # 解析生成的对话内容
    private function parseGeneratedDialogue($content)
    {
        $dialogues = [];

        // 尝试解析JSON格式
        $regex = '/\{\s*"role":\s*"(.*?)",\s*"dialogue":\s*"(.*?)"\s*\}/s';
        preg_match_all($regex, $content, $matches, PREG_SET_ORDER);

        if (!empty($matches)) {
            foreach ($matches as $index => $match) {
                $dialogues[] = [
                    'sort_order' => $index,
                    'role' => $match[1],
                    'content_zh' => $match[2],
                    'content_vi' => '' // 暂时不翻译
                ];
            }
        } else {
            // 如果JSON解析失败，尝试按行解析
            $lines = explode("\n", $content);
            $order = 0;
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // 简单的角色判断
                $role = ($order % 2 === 0) ? 'system' : 'user';
                $dialogues[] = [
                    'sort_order' => $order,
                    'role' => $role,
                    'content_zh' => $line,
                    'content_vi' => ''
                ];
                $order++;
            }
        }

        return $dialogues;
    }

    # 获取演示对话数据
    private function getDemoDialogue($mode, $model, $topicText, $difficulty)
    {
        // 根据不同话题和模式生成不同的演示内容
        $demoContents = [
            '导游中文' => [
                'scene' => '{ "role": "system", "dialogue": "大家好，我是今天的导游阿玲。" }\n{ "role": "user", "dialogue": "阿玲你好，今天我们去哪里玩呀？" }\n{ "role": "system", "dialogue": "今天我们会去河内的还剑湖。这里很有名，也很漂亮。" }\n{ "role": "user", "dialogue": "还剑湖有什么特别的吗？" }\n{ "role": "system", "dialogue": "还剑湖中心有龟塔，湖水很清，还有很多故事，值得一看。" }',
                'free' => '{ "role": "user", "dialogue": "你好，我想了解一下越南的旅游景点。" }\n{ "role": "system", "dialogue": "你好！越南有很多美丽的景点，你对哪个城市比较感兴趣？" }\n{ "role": "user", "dialogue": "我想去河内，听说那里很有特色。" }\n{ "role": "system", "dialogue": "河内确实是个很棒的选择！那里有还剑湖、文庙、老街区，还有美味的越南粉。" }'
            ],
            '商务中文' => [
                'scene' => '{ "role": "system", "dialogue": "您好，欢迎来到我们公司，我是商务代表小李。" }\n{ "role": "user", "dialogue": "小李你好，我们想了解一下你们的产品。" }\n{ "role": "system", "dialogue": "好的，我们主要生产电子产品，包括手机配件和智能设备。" }\n{ "role": "user", "dialogue": "价格怎么样？有优惠吗？" }\n{ "role": "system", "dialogue": "我们的价格很有竞争力，大量订购可以享受折扣。" }',
                'free' => '{ "role": "user", "dialogue": "我们公司想找一个长期合作伙伴。" }\n{ "role": "system", "dialogue": "非常好！我们也希望建立长期的合作关系。您的公司主要做什么业务？" }\n{ "role": "user", "dialogue": "我们做电子商务，需要大量的手机配件。" }\n{ "role": "system", "dialogue": "太好了！这正好是我们的专业领域。我们可以提供定制化的解决方案。" }'
            ],
            '工厂中文' => [
                'scene' => '{ "role": "system", "dialogue": "早上好，欢迎来到我们工厂参观。" }\n{ "role": "user", "dialogue": "早上好，请问这里主要生产什么？" }\n{ "role": "system", "dialogue": "我们主要生产电子元件，每天可以生产一万件。" }\n{ "role": "user", "dialogue": "这里有多少工人？" }\n{ "role": "system", "dialogue": "我们有200名工人，分三班工作，24小时生产。" }',
                'free' => '{ "role": "user", "dialogue": "我想了解一下你们工厂的生产能力。" }\n{ "role": "system", "dialogue": "我们是一家现代化的电子工厂，有先进的生产线。" }\n{ "role": "user", "dialogue": "你们的产品质量怎么样？" }\n{ "role": "system", "dialogue": "我们通过了ISO9001认证，产品合格率达到99.5%以上。" }'
            ]
        ];

        // 选择对应的演示内容
        $topicKey = array_key_exists($topicText, $demoContents) ? $topicText : '导游中文';
        $demoContent = $demoContents[$topicKey][$mode] ?? $demoContents['导游中文']['scene'];

        $dialogues = $this->parseGeneratedDialogue($demoContent);

        return json([
            'success' => true,
            'data' => [
                'raw_content' => $demoContent,
                'dialogues' => $dialogues,
                'mode' => $mode,
                'model' => $model . ' (演示模式)',
                'topic' => $topicText,
                'difficulty' => $difficulty,
                'prompt_module' => 'demo_mode',
                'is_demo' => true
            ]
        ]);
    }

}
