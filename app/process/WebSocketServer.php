<?php

namespace app\process;

use Workerman\Worker;
use Workerman\Connection\TcpConnection;
use support\Log;
use support\Redis;

class WebSocketServer
{
    /**
     * @var array 存储连接的客户端
     */
    private static $connections = [];

    /**
     * @var array 存储任务ID与连接的映射
     */
    private static $taskConnections = [];

    public function onWorkerStart(Worker $worker)
    {
        Log::info('WebSocket server started on ' . $worker->getSocketName());
        
        // 监听Redis频道，接收队列处理结果
        $this->subscribeToRedis();
    }

    public function onConnect(TcpConnection $connection)
    {
        Log::info('New WebSocket connection: ' . $connection->id);
        self::$connections[$connection->id] = $connection;
        
        // 发送连接成功消息
        $connection->send(json_encode([
            'type' => 'connected',
            'message' => 'WebSocket连接成功',
            'connection_id' => $connection->id
        ]));
    }

    public function onMessage(TcpConnection $connection, $data)
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message || !isset($message['type'])) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '无效的消息格式'
                ]));
                return;
            }

            switch ($message['type']) {
                case 'subscribe':
                    // 客户端订阅特定任务
                    if (isset($message['task_id'])) {
                        self::$taskConnections[$message['task_id']] = $connection->id;
                        $connection->send(json_encode([
                            'type' => 'subscribed',
                            'task_id' => $message['task_id'],
                            'message' => '已订阅任务: ' . $message['task_id']
                        ]));
                        Log::info("Connection {$connection->id} subscribed to task {$message['task_id']}");
                    }
                    break;
                    
                case 'ping':
                    $connection->send(json_encode([
                        'type' => 'pong',
                        'timestamp' => time()
                    ]));
                    break;
                    
                default:
                    $connection->send(json_encode([
                        'type' => 'error',
                        'message' => '未知的消息类型: ' . $message['type']
                    ]));
            }
        } catch (\Exception $e) {
            Log::error('WebSocket message error: ' . $e->getMessage());
            $connection->send(json_encode([
                'type' => 'error',
                'message' => '消息处理失败'
            ]));
        }
    }

    public function onClose(TcpConnection $connection)
    {
        Log::info('WebSocket connection closed: ' . $connection->id);
        
        // 清理连接记录
        unset(self::$connections[$connection->id]);
        
        // 清理任务订阅记录
        foreach (self::$taskConnections as $taskId => $connectionId) {
            if ($connectionId === $connection->id) {
                unset(self::$taskConnections[$taskId]);
            }
        }
    }

    public function onError(TcpConnection $connection, $code, $msg)
    {
        Log::error("WebSocket error on connection {$connection->id}: $code - $msg");
    }

    /**
     * 监听Redis频道，接收队列处理结果
     */
    private function subscribeToRedis()
    {
        // 在实际项目中，这里应该使用Redis的pub/sub功能
        // 由于webman的Redis配置限制，我们使用定时器轮询的方式
        \Workerman\Timer::add(1, function() {
            $this->checkTaskResults();
        });
    }

    /**
     * 检查任务结果并推送给客户端
     */
    private function checkTaskResults()
    {
        try {
            $redis = Redis::connection();
            
            // 检查所有待处理的任务结果
            foreach (self::$taskConnections as $taskId => $connectionId) {
                if (!isset(self::$connections[$connectionId])) {
                    // 连接已断开，清理记录
                    unset(self::$taskConnections[$taskId]);
                    continue;
                }
                
                // 检查任务进度
                $progressKey = "task_progress:$taskId";
                $progress = $redis->get($progressKey);
                if ($progress) {
                    $progressData = json_decode($progress, true);
                    $this->sendToConnection($connectionId, [
                        'type' => 'progress',
                        'task_id' => $taskId,
                        'data' => $progressData
                    ]);
                    
                    // 如果任务完成，清理进度数据
                    if (isset($progressData['status']) && $progressData['status'] === 'completed') {
                        $redis->del($progressKey);
                        unset(self::$taskConnections[$taskId]);
                    }
                }
                
                // 检查任务结果
                $resultKey = "task_result:$taskId";
                $result = $redis->get($resultKey);
                if ($result) {
                    $resultData = json_decode($result, true);
                    $this->sendToConnection($connectionId, [
                        'type' => 'result',
                        'task_id' => $taskId,
                        'data' => $resultData
                    ]);
                    
                    // 清理结果数据
                    $redis->del($resultKey);
                    unset(self::$taskConnections[$taskId]);
                }
                
                // 检查任务错误
                $errorKey = "task_error:$taskId";
                $error = $redis->get($errorKey);
                if ($error) {
                    $errorData = json_decode($error, true);
                    $this->sendToConnection($connectionId, [
                        'type' => 'error',
                        'task_id' => $taskId,
                        'data' => $errorData
                    ]);
                    
                    // 清理错误数据
                    $redis->del($errorKey);
                    unset(self::$taskConnections[$taskId]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Redis check error: ' . $e->getMessage());
        }
    }

    /**
     * 向指定连接发送消息
     */
    private function sendToConnection($connectionId, $data)
    {
        if (isset(self::$connections[$connectionId])) {
            try {
                self::$connections[$connectionId]->send(json_encode($data));
            } catch (\Exception $e) {
                Log::error("Failed to send message to connection $connectionId: " . $e->getMessage());
                // 连接可能已断开，清理记录
                unset(self::$connections[$connectionId]);
            }
        }
    }

    /**
     * 广播消息给所有连接
     */
    public static function broadcast($data)
    {
        $message = json_encode($data);
        foreach (self::$connections as $connection) {
            try {
                $connection->send($message);
            } catch (\Exception $e) {
                Log::error('Broadcast error: ' . $e->getMessage());
            }
        }
    }

    /**
     * 向特定任务的订阅者发送消息
     */
    public static function sendToTask($taskId, $data)
    {
        if (isset(self::$taskConnections[$taskId]) && isset(self::$connections[self::$taskConnections[$taskId]])) {
            try {
                $connection = self::$connections[self::$taskConnections[$taskId]];
                $connection->send(json_encode($data));
            } catch (\Exception $e) {
                Log::error("Failed to send message to task $taskId: " . $e->getMessage());
            }
        }
    }
}
