<?php

namespace app\queue\redis\generate;

use Webman\RedisQueue\Consumer;
use support\Log;
use support\Redis;
use app\service\ait\Ai;
use app\model\AiPromptTemplates;
use app\model\LearningTopics;

class GenerateTestTask implements Consumer
{
    // 要消费的队列名
    public $queue = 'generate_test_task';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接
    public $connection = 'default';

    // 消费任务
    public function consume($data)
    {
        $taskId = $data['task_id'];
        $mode = $data['mode'];
        $model = $data['model'];
        $topicText = $data['topic_text'];
        $difficulty = $data['difficulty'];
        $promptTemplate = $data['prompt_template'];
        $userContent = $data['user_content'];
        $promptInfo = $data['prompt_info'];
        $demo = $data['demo'] ?? false;

        Log::info("开始处理AI对话生成任务: $taskId");

        try {
            // 发送开始处理的进度
            $this->updateProgress($taskId, [
                'status' => 'processing',
                'message' => '正在生成对话内容...',
                'progress' => 10
            ]);

            if ($demo) {
                // 演示模式，返回模拟数据
                $this->handleDemoMode($taskId, $mode, $model, $topicText, $difficulty);
                return;
            }

            // 检查AI配置
            if (!env('AI_API_KEY') || !env('AI_API')) {
                throw new \Exception('AI服务未配置');
            }

            // 更新进度
            $this->updateProgress($taskId, [
                'status' => 'processing',
                'message' => '正在调用AI服务...',
                'progress' => 30
            ]);

            // 构建AI请求数据
            $aiData = [
                'model' => $model !== 'gpt-4o-mini' ? $model : env('AI_MODEL', 'gpt-4o-mini'),
                'temperature' => 0.8,
                'messages' => [
                    ['role' => 'system', 'content' => $promptTemplate],
                    ['role' => 'user', 'content' => $userContent]
                ],
            ];

            // 调用AI服务
            $ai = new Ai();
            $ai->completions($aiData, function ($response) use ($taskId, $mode, $model, $topicText, $difficulty, $promptInfo) {
                try {
                    // 更新进度
                    $this->updateProgress($taskId, [
                        'status' => 'processing',
                        'message' => '正在解析AI响应...',
                        'progress' => 70
                    ]);

                    $content = $response['choices'][0]['message']['content'];
                    $dialogues = $this->parseGeneratedDialogue($content);

                    // 更新进度
                    $this->updateProgress($taskId, [
                        'status' => 'processing',
                        'message' => '正在整理结果...',
                        'progress' => 90
                    ]);

                    // 构建最终结果
                    $result = [
                        'success' => true,
                        'data' => [
                            'raw_content' => $content,
                            'dialogues' => $dialogues,
                            'mode' => $mode,
                            'model' => $model,
                            'topic' => $topicText,
                            'difficulty' => $difficulty,
                            'prompt_module' => $promptInfo['module'],
                            'prompt_info' => $promptInfo,
                            'is_demo' => false,
                            'task_id' => $taskId,
                            'generated_at' => date('Y-m-d H:i:s')
                        ]
                    ];

                    // 发送最终结果
                    $this->sendResult($taskId, $result);

                    Log::info("AI对话生成任务完成: $taskId");

                } catch (\Exception $e) {
                    Log::error("AI响应处理失败: " . $e->getMessage());
                    $this->sendError($taskId, [
                        'error' => 'AI响应处理失败：' . $e->getMessage(),
                        'task_id' => $taskId
                    ]);
                }
            });

        } catch (\Exception $e) {
            Log::error("AI对话生成任务失败: " . $e->getMessage());
            $this->sendError($taskId, [
                'error' => 'AI对话生成失败：' . $e->getMessage(),
                'task_id' => $taskId
            ]);
        }
    }

    /**
     * 处理演示模式
     */
    private function handleDemoMode($taskId, $mode, $model, $topicText, $difficulty)
    {
        // 模拟处理时间
        sleep(2);

        // 更新进度
        $this->updateProgress($taskId, [
            'status' => 'processing',
            'message' => '正在生成演示数据...',
            'progress' => 50
        ]);

        sleep(1);

        // 根据不同话题和模式生成不同的演示内容
        $demoContents = [
            '导游中文' => [
                'scene' => '{ "role": "system", "dialogue": "大家好，我是今天的导游阿玲。" }\n{ "role": "user", "dialogue": "阿玲你好，今天我们去哪里玩呀？" }\n{ "role": "system", "dialogue": "今天我们会去河内的还剑湖。这里很有名，也很漂亮。" }\n{ "role": "user", "dialogue": "还剑湖有什么特别的吗？" }\n{ "role": "system", "dialogue": "还剑湖中心有龟塔，湖水很清，还有很多故事，值得一看。" }',
                'free' => '{ "role": "user", "dialogue": "你好，我想了解一下越南的旅游景点。" }\n{ "role": "system", "dialogue": "你好！越南有很多美丽的景点，你对哪个城市比较感兴趣？" }\n{ "role": "user", "dialogue": "我想去河内，听说那里很有特色。" }\n{ "role": "system", "dialogue": "河内确实是个很棒的选择！那里有还剑湖、文庙、老街区，还有美味的越南粉。" }'
            ],
            '商务中文' => [
                'scene' => '{ "role": "system", "dialogue": "您好，欢迎来到我们公司，我是商务代表小李。" }\n{ "role": "user", "dialogue": "小李你好，我们想了解一下你们的产品。" }\n{ "role": "system", "dialogue": "好的，我们主要生产电子产品，包括手机配件和智能设备。" }\n{ "role": "user", "dialogue": "价格怎么样？有优惠吗？" }\n{ "role": "system", "dialogue": "我们的价格很有竞争力，大量订购可以享受折扣。" }',
                'free' => '{ "role": "user", "dialogue": "我们公司想找一个长期合作伙伴。" }\n{ "role": "system", "dialogue": "非常好！我们也希望建立长期的合作关系。您的公司主要做什么业务？" }\n{ "role": "user", "dialogue": "我们做电子商务，需要大量的手机配件。" }\n{ "role": "system", "dialogue": "太好了！这正好是我们的专业领域。我们可以提供定制化的解决方案。" }'
            ],
            '工厂中文' => [
                'scene' => '{ "role": "system", "dialogue": "早上好，欢迎来到我们工厂参观。" }\n{ "role": "user", "dialogue": "早上好，请问这里主要生产什么？" }\n{ "role": "system", "dialogue": "我们主要生产电子元件，每天可以生产一万件。" }\n{ "role": "user", "dialogue": "这里有多少工人？" }\n{ "role": "system", "dialogue": "我们有200名工人，分三班工作，24小时生产。" }',
                'free' => '{ "role": "user", "dialogue": "我想了解一下你们工厂的生产能力。" }\n{ "role": "system", "dialogue": "我们是一家现代化的电子工厂，有先进的生产线。" }\n{ "role": "user", "dialogue": "你们的产品质量怎么样？" }\n{ "role": "system", "dialogue": "我们通过了ISO9001认证，产品合格率达到99.5%以上。" }'
            ]
        ];

        // 选择对应的演示内容
        $topicKey = array_key_exists($topicText, $demoContents) ? $topicText : '导游中文';
        $demoContent = $demoContents[$topicKey][$mode] ?? $demoContents['导游中文']['scene'];

        $dialogues = $this->parseGeneratedDialogue($demoContent);

        // 更新进度
        $this->updateProgress($taskId, [
            'status' => 'processing',
            'message' => '演示数据生成完成',
            'progress' => 90
        ]);

        $result = [
            'success' => true,
            'data' => [
                'raw_content' => $demoContent,
                'dialogues' => $dialogues,
                'mode' => $mode,
                'model' => $model . ' (演示模式)',
                'topic' => $topicText,
                'difficulty' => $difficulty,
                'prompt_module' => 'demo_mode',
                'is_demo' => true,
                'task_id' => $taskId,
                'generated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->sendResult($taskId, $result);
        Log::info("演示模式任务完成: $taskId");
    }

    /**
     * 解析生成的对话内容
     */
    private function parseGeneratedDialogue($content)
    {
        $dialogues = [];

        // 尝试解析JSON格式
        $regex = '/\{\s*"role":\s*"(.*?)",\s*"dialogue":\s*"(.*?)"\s*\}/s';
        preg_match_all($regex, $content, $matches, PREG_SET_ORDER);

        if (!empty($matches)) {
            foreach ($matches as $index => $match) {
                $dialogues[] = [
                    'sort_order' => $index,
                    'role' => $match[1],
                    'content_zh' => $match[2],
                    'content_vi' => '' // 暂时不翻译
                ];
            }
        } else {
            // 如果JSON解析失败，尝试按行解析
            $lines = explode("\n", $content);
            $order = 0;
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // 简单的角色判断
                $role = ($order % 2 === 0) ? 'system' : 'user';
                $dialogues[] = [
                    'sort_order' => $order,
                    'role' => $role,
                    'content_zh' => $line,
                    'content_vi' => ''
                ];
                $order++;
            }
        }

        return $dialogues;
    }

    /**
     * 更新任务进度
     */
    private function updateProgress($taskId, $progressData)
    {
        try {
            $redis = Redis::connection();
            $redis->setex("task_progress:$taskId", 300, json_encode($progressData)); // 5分钟过期
        } catch (\Exception $e) {
            Log::error("更新任务进度失败: " . $e->getMessage());
        }
    }

    /**
     * 发送最终结果
     */
    private function sendResult($taskId, $result)
    {
        try {
            $redis = Redis::connection();
            $redis->setex("task_result:$taskId", 300, json_encode($result)); // 5分钟过期
            
            // 发送完成进度
            $this->updateProgress($taskId, [
                'status' => 'completed',
                'message' => '任务完成',
                'progress' => 100
            ]);
        } catch (\Exception $e) {
            Log::error("发送任务结果失败: " . $e->getMessage());
        }
    }

    /**
     * 发送错误信息
     */
    private function sendError($taskId, $error)
    {
        try {
            $redis = Redis::connection();
            $redis->setex("task_error:$taskId", 300, json_encode($error)); // 5分钟过期
        } catch (\Exception $e) {
            Log::error("发送任务错误失败: " . $e->getMessage());
        }
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        Log::error("AI对话生成任务消费失败: " . $e->getMessage());
        Log::error("Package: " . json_encode($package));
        
        // 尝试从包中获取任务ID并发送错误
        if (isset($package['task_id'])) {
            $this->sendError($package['task_id'], [
                'error' => '任务处理失败：' . $e->getMessage(),
                'task_id' => $package['task_id']
            ]);
        }
    }
}
