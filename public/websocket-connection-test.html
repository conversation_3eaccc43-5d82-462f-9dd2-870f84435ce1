<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .url-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .url-item {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试工具</h1>
        
        <div id="status" class="status info">准备测试WebSocket连接...</div>
        
        <div class="url-list">
            <h3>将要测试的WebSocket地址：</h3>
            <div id="urlList"></div>
        </div>
        
        <button onclick="startTest()">开始测试</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let testResults = [];
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            testResults = [];
        }
        
        function generateTestUrls() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const hostname = window.location.hostname;
            const host = window.location.host;
            
            const urls = [
                `${protocol}//${hostname}:8080`,
                `${protocol}//${host.split(':')[0]}:8080`,
                `ws://${hostname}:8080`,
                `ws://localhost:8080`,
                `ws://127.0.0.1:8080`
            ];
            
            // 去重
            return [...new Set(urls)];
        }
        
        function displayUrls() {
            const urls = generateTestUrls();
            const urlListElement = document.getElementById('urlList');
            urlListElement.innerHTML = urls.map(url => 
                `<div class="url-item">${url}</div>`
            ).join('');
        }
        
        function testWebSocketConnection(url) {
            return new Promise((resolve) => {
                log(`测试连接: ${url}`);
                
                const ws = new WebSocket(url);
                const timeout = setTimeout(() => {
                    ws.close();
                    resolve({
                        url: url,
                        success: false,
                        error: '连接超时'
                    });
                }, 5000);
                
                ws.onopen = function() {
                    clearTimeout(timeout);
                    log(`✅ 连接成功: ${url}`);
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: Date.now()
                    }));
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 收到消息: ${JSON.stringify(data)}`);
                        
                        ws.close();
                        resolve({
                            url: url,
                            success: true,
                            message: data
                        });
                    } catch (e) {
                        log(`❌ 消息解析失败: ${e.message}`);
                        ws.close();
                        resolve({
                            url: url,
                            success: false,
                            error: '消息解析失败'
                        });
                    }
                };
                
                ws.onerror = function(error) {
                    clearTimeout(timeout);
                    log(`❌ 连接错误: ${url} - ${error.type}`);
                    resolve({
                        url: url,
                        success: false,
                        error: error.type || '连接错误'
                    });
                };
                
                ws.onclose = function(event) {
                    clearTimeout(timeout);
                    if (event.code !== 1000) {
                        log(`❌ 连接关闭: ${url} - Code: ${event.code}, Reason: ${event.reason}`);
                        resolve({
                            url: url,
                            success: false,
                            error: `连接关闭 (${event.code})`
                        });
                    }
                };
            });
        }
        
        async function startTest() {
            clearLog();
            updateStatus('正在测试WebSocket连接...', 'info');
            
            const urls = generateTestUrls();
            log(`开始测试 ${urls.length} 个WebSocket地址...`);
            log('');
            
            testResults = [];
            
            for (const url of urls) {
                const result = await testWebSocketConnection(url);
                testResults.push(result);
                
                // 等待一秒再测试下一个
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // 显示测试总结
            log('');
            log('=== 测试总结 ===');
            
            const successfulConnections = testResults.filter(r => r.success);
            const failedConnections = testResults.filter(r => !r.success);
            
            if (successfulConnections.length > 0) {
                updateStatus(`测试完成！找到 ${successfulConnections.length} 个可用连接`, 'success');
                log(`✅ 可用连接 (${successfulConnections.length}个):`);
                successfulConnections.forEach(result => {
                    log(`   ${result.url}`);
                });
            } else {
                updateStatus('测试完成！没有找到可用的WebSocket连接', 'error');
            }
            
            if (failedConnections.length > 0) {
                log(`❌ 失败连接 (${failedConnections.length}个):`);
                failedConnections.forEach(result => {
                    log(`   ${result.url} - ${result.error}`);
                });
            }
            
            log('');
            log('=== 建议 ===');
            if (successfulConnections.length > 0) {
                log('✅ WebSocket服务运行正常，可以使用异步功能');
            } else {
                log('❌ WebSocket服务不可用，请检查：');
                log('   1. 服务器是否启动了WebSocket服务 (端口8080)');
                log('   2. 防火墙是否开放了8080端口');
                log('   3. 网络是否可以访问服务器的8080端口');
                log('   4. 如果使用代理，是否正确配置了WebSocket代理');
            }
        }
        
        // 页面加载时显示要测试的URL
        window.onload = function() {
            displayUrls();
        };
    </script>
</body>
</html>
