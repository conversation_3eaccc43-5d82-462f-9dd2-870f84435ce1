<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话生成 - WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.processing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .result {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .dialogue-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .dialogue-system {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .dialogue-user {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI对话生成 - WebSocket异步测试</h1>
        
        <div class="form-group">
            <label for="mode">模式:</label>
            <select id="mode">
                <option value="scene">场景对话</option>
                <option value="free">自由对话</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="difficulty">难度:</label>
            <select id="difficulty">
                <option value="easy">简单</option>
                <option value="medium">中等</option>
                <option value="hard">困难</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="custom_topic">自定义话题:</label>
            <input type="text" id="custom_topic" placeholder="留空使用默认话题">
        </div>
        
        <div class="form-group">
            <label for="demo">
                <input type="checkbox" id="demo" checked> 演示模式
            </label>
        </div>
        
        <button onclick="startGeneration()">开始生成</button>
        <button onclick="connectWebSocket()" id="connectBtn">连接WebSocket</button>
        <button onclick="disconnectWebSocket()" id="disconnectBtn" disabled>断开连接</button>
        
        <div id="status" class="status disconnected">WebSocket未连接</div>
        
        <div class="progress" style="display: none;" id="progressContainer">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div id="progressText" style="display: none;"></div>
        
        <div id="result" style="display: none;"></div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        let currentTaskId = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, className) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${className}`;
        }
        
        function updateProgress(progress, message) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            if (progress > 0) {
                progressContainer.style.display = 'block';
                progressText.style.display = 'block';
                progressBar.style.width = progress + '%';
                progressText.textContent = `${message} (${progress}%)`;
            } else {
                progressContainer.style.display = 'none';
                progressText.style.display = 'none';
            }
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接');
                return;
            }
            
            ws = new WebSocket('ws://localhost:8080');
            
            ws.onopen = function() {
                log('WebSocket连接成功');
                updateStatus('WebSocket已连接', 'connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data)}`);
                    
                    switch(data.type) {
                        case 'connected':
                            log('服务器确认连接');
                            break;
                            
                        case 'subscribed':
                            log(`已订阅任务: ${data.task_id}`);
                            break;
                            
                        case 'progress':
                            updateProgress(data.data.progress, data.data.message);
                            updateStatus(`处理中: ${data.data.message}`, 'processing');
                            break;
                            
                        case 'result':
                            displayResult(data.data);
                            updateProgress(0, '');
                            updateStatus('任务完成', 'connected');
                            break;
                            
                        case 'error':
                            log(`错误: ${JSON.stringify(data.data)}`);
                            updateProgress(0, '');
                            updateStatus('任务失败', 'disconnected');
                            break;
                            
                        case 'pong':
                            log('收到心跳响应');
                            break;
                    }
                } catch (e) {
                    log(`解析消息失败: ${e.message}`);
                }
            };
            
            ws.onclose = function() {
                log('WebSocket连接关闭');
                updateStatus('WebSocket已断开', 'disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            };
            
            ws.onerror = function(error) {
                log(`WebSocket错误: ${error}`);
                updateStatus('WebSocket连接错误', 'disconnected');
            };
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function startGeneration() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接WebSocket');
                return;
            }
            
            const formData = {
                mode: document.getElementById('mode').value,
                difficulty: document.getElementById('difficulty').value,
                custom_topic: document.getElementById('custom_topic').value,
                demo: document.getElementById('demo').checked
            };
            
            log(`开始生成任务，参数: ${JSON.stringify(formData)}`);
            updateStatus('提交任务中...', 'processing');
            
            // 调用后端API
            fetch('/generateTest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                log(`API响应: ${JSON.stringify(data)}`);
                
                if (data.success && data.async) {
                    currentTaskId = data.task_id;
                    log(`获得任务ID: ${currentTaskId}`);
                    
                    // 订阅任务
                    const subscribeMessage = {
                        type: 'subscribe',
                        task_id: currentTaskId
                    };
                    ws.send(JSON.stringify(subscribeMessage));
                    log(`发送订阅消息: ${JSON.stringify(subscribeMessage)}`);
                    
                    updateStatus('等待任务处理...', 'processing');
                } else {
                    log(`任务提交失败: ${data.error || '未知错误'}`);
                    updateStatus('任务提交失败', 'disconnected');
                }
            })
            .catch(error => {
                log(`API调用失败: ${error.message}`);
                updateStatus('API调用失败', 'disconnected');
            });
        }
        
        function displayResult(resultData) {
            const resultElement = document.getElementById('result');
            
            if (resultData.success && resultData.data) {
                const data = resultData.data;
                let html = `
                    <div class="result">
                        <h3>生成结果</h3>
                        <p><strong>任务ID:</strong> ${data.task_id || 'N/A'}</p>
                        <p><strong>模式:</strong> ${data.mode}</p>
                        <p><strong>话题:</strong> ${data.topic}</p>
                        <p><strong>难度:</strong> ${data.difficulty}</p>
                        <p><strong>模型:</strong> ${data.model}</p>
                        <p><strong>生成时间:</strong> ${data.generated_at || 'N/A'}</p>
                        <p><strong>演示模式:</strong> ${data.is_demo ? '是' : '否'}</p>
                        
                        <h4>对话内容:</h4>
                `;
                
                if (data.dialogues && data.dialogues.length > 0) {
                    data.dialogues.forEach(dialogue => {
                        const className = dialogue.role === 'system' ? 'dialogue-system' : 'dialogue-user';
                        html += `
                            <div class="dialogue-item ${className}">
                                <strong>${dialogue.role === 'system' ? '系统' : '用户'}:</strong>
                                ${dialogue.content_zh}
                            </div>
                        `;
                    });
                } else {
                    html += '<p>没有生成对话内容</p>';
                }
                
                html += '</div>';
                resultElement.innerHTML = html;
                resultElement.style.display = 'block';
            } else {
                resultElement.innerHTML = `<div class="result"><h3>生成失败</h3><p>${JSON.stringify(resultData)}</p></div>`;
                resultElement.style.display = 'block';
            }
        }
        
        // 页面加载时自动连接WebSocket
        window.onload = function() {
            log('页面加载完成');
            // connectWebSocket(); // 可以取消注释来自动连接
        };
        
        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            disconnectWebSocket();
        };
    </script>
</body>
</html>
