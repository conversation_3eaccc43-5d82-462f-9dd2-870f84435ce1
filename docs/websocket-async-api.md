# AI对话生成异步API文档

## 概述

`generateTest` 方法已经从同步处理改为异步处理，使用 Redis 队列和 WebSocket 进行实时通信。

## 架构变更

### 原架构
- 前端发送请求 → 后端直接处理AI请求 → 返回结果

### 新架构
- 前端发送请求 → 后端生成任务ID并投递到队列 → 立即返回任务ID
- 队列消费者异步处理AI请求 → 通过Redis存储进度和结果
- WebSocket服务器监听Redis → 实时推送进度和结果给前端

## API变更

### 请求参数
保持不变，仍然支持以下参数：
- `mode`: 'scene' 或 'free'
- `model`: AI模型名称
- `topic_id`: 话题ID
- `difficulty`: 'easy', 'medium', 'hard'
- `custom_topic`: 自定义话题
- `demo`: 演示模式
- `custom_prompt`: 自定义提示词

### 响应格式

#### 成功响应
```json
{
    "success": true,
    "async": true,
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
    "websocket_url": "ws://localhost:8080",
    "message": "任务已提交，请通过WebSocket连接获取实时进度和结果",
    "instructions": [
        "1. 连接到WebSocket: ws://localhost:8080",
        "2. 发送订阅消息: {\"type\":\"subscribe\",\"task_id\":\"generate_test_64f8a1b2c3d4e_1703123456\"}",
        "3. 监听进度消息: type=\"progress\"",
        "4. 监听结果消息: type=\"result\"",
        "5. 监听错误消息: type=\"error\""
    ]
}
```

#### 错误响应
```json
{
    "error": "错误信息"
}
```

## WebSocket通信协议

### 连接
```
ws://localhost:8080
```

### 消息格式

#### 客户端发送消息

1. **订阅任务**
```json
{
    "type": "subscribe",
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456"
}
```

2. **心跳检测**
```json
{
    "type": "ping"
}
```

#### 服务器推送消息

1. **连接确认**
```json
{
    "type": "connected",
    "message": "WebSocket连接成功",
    "connection_id": "connection_123"
}
```

2. **订阅确认**
```json
{
    "type": "subscribed",
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
    "message": "已订阅任务: generate_test_64f8a1b2c3d4e_1703123456"
}
```

3. **进度更新**
```json
{
    "type": "progress",
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
    "data": {
        "status": "processing",
        "message": "正在生成对话内容...",
        "progress": 30
    }
}
```

4. **最终结果**
```json
{
    "type": "result",
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
    "data": {
        "success": true,
        "data": {
            "raw_content": "生成的原始内容",
            "dialogues": [
                {
                    "sort_order": 0,
                    "role": "system",
                    "content_zh": "对话内容",
                    "content_vi": ""
                }
            ],
            "mode": "scene",
            "model": "gpt-4o-mini",
            "topic": "导游中文",
            "difficulty": "easy",
            "prompt_module": "dialogue_generation_dyzw",
            "is_demo": false,
            "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
            "generated_at": "2023-12-21 10:30:45"
        }
    }
}
```

5. **错误消息**
```json
{
    "type": "error",
    "task_id": "generate_test_64f8a1b2c3d4e_1703123456",
    "data": {
        "error": "错误信息",
        "task_id": "generate_test_64f8a1b2c3d4e_1703123456"
    }
}
```

6. **心跳响应**
```json
{
    "type": "pong",
    "timestamp": 1703123456
}
```

## 前端集成示例

### JavaScript示例
```javascript
// 1. 提交任务
fetch('/generateTest', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        mode: 'scene',
        difficulty: 'easy',
        demo: true
    })
})
.then(response => response.json())
.then(data => {
    if (data.success && data.async) {
        const taskId = data.task_id;
        
        // 2. 连接WebSocket
        const ws = new WebSocket('ws://localhost:8080');
        
        ws.onopen = function() {
            // 3. 订阅任务
            ws.send(JSON.stringify({
                type: 'subscribe',
                task_id: taskId
            }));
        };
        
        ws.onmessage = function(event) {
            const message = JSON.parse(event.data);
            
            switch(message.type) {
                case 'progress':
                    console.log('进度:', message.data.progress + '%');
                    break;
                case 'result':
                    console.log('结果:', message.data);
                    break;
                case 'error':
                    console.error('错误:', message.data);
                    break;
            }
        };
    }
});
```

## 部署说明

### 1. 启动WebSocket服务器
WebSocket服务器会随着webman一起启动，监听端口8080。

### 2. 启动队列消费者
队列消费者也会自动启动，处理`generate_test_task`队列。

### 3. 测试页面
访问 `http://localhost:9696/websocket-test.html` 进行测试。

## 配置说明

### WebSocket服务器配置
在 `config/process.php` 中：
```php
'websocket' => [
    'handler' => app\process\WebSocketServer::class,
    'listen' => 'websocket://0.0.0.0:8080',
    'count' => 1,
],
```

### 队列消费者配置
在 `config/plugin/webman/redis-queue/process.php` 中：
```php
'generate_test_task' => [
    'handler' => Webman\RedisQueue\Process\Consumer::class,
    'count' => 5,
    'constructor' => [
        'consumer_dir' => app_path() . '/queue/redis/generate'
    ]
],
```

## 优势

1. **非阻塞**: 前端不需要等待AI处理完成
2. **实时反馈**: 通过WebSocket实时获取处理进度
3. **可扩展**: 可以轻松增加队列消费者数量
4. **容错性**: 任务失败不会影响其他请求
5. **用户体验**: 用户可以看到处理进度，不会感觉卡顿

## 注意事项

1. WebSocket连接需要保持活跃
2. 任务结果在Redis中有5分钟的过期时间
3. 确保Redis服务正常运行
4. 演示模式仍然可以正常使用
