# WebSocket 部署配置指南

## 问题描述

在服务器部署环境中，WebSocket连接可能会遇到以下问题：
- `WebSocket connection to 'ws://localhost:8080/' failed`
- 客户端无法连接到服务器的WebSocket服务

## 解决方案

### 1. 动态WebSocket URL

后端API现在会根据当前请求动态生成WebSocket URL：

```php
// 构建WebSocket URL - 根据当前请求动态生成
$host = $request->host();
$scheme = $request->getScheme() === 'https' ? 'wss' : 'ws';
$websocketUrl = $scheme . '://' . $host . ':8080';
```

### 2. 前端重试机制

前端增加了智能重试机制，会尝试多种连接方式：

1. **第一次尝试**: 使用服务器返回的WebSocket URL
2. **第二次重试**: 使用当前域名 + 8080端口
3. **第三次重试**: 使用IP地址 + 8080端口
4. **第四次重试**: 使用localhost（开发环境）

### 3. 服务器配置

#### WebSocket服务器配置
```php
// config/process.php
'websocket' => [
    'handler' => app\process\WebSocketServer::class,
    'listen' => 'websocket://0.0.0.0:8080',  // 监听所有接口
    'count' => 1,
    'context' => [
        'socket' => [
            'so_reuseport' => 1,
            'so_keepalive' => 1,
        ]
    ]
],
```

#### 防火墙配置
确保服务器防火墙开放8080端口：

```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload

# 或者使用iptables
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
```

#### Nginx反向代理（可选）

如果使用Nginx，可以配置WebSocket代理：

```nginx
# /etc/nginx/sites-available/your-site
server {
    listen 80;
    server_name your-domain.com;
    
    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:9696;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

如果使用Nginx代理，需要修改WebSocket URL：
```php
// 在IndexController.php中
$websocketUrl = $scheme . '://' . $host . '/ws';
```

### 4. HTTPS/WSS支持

对于HTTPS站点，WebSocket会自动使用WSS协议：

```javascript
// 前端自动检测协议
const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
const websocketUrl = `${protocol}//${host}:8080`;
```

### 5. 云服务器配置

#### 阿里云/腾讯云
1. 在安全组中开放8080端口
2. 确保服务器内部防火墙也开放了8080端口

#### AWS/Azure
1. 在Security Groups中添加8080端口规则
2. 确保Network ACLs允许8080端口流量

### 6. Docker部署

如果使用Docker，确保端口映射正确：

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "9696:9696"  # HTTP端口
      - "8080:8080"  # WebSocket端口
    environment:
      - AI_API_KEY=your_key
      - AI_API=your_api_url
```

### 7. 测试连接

#### 使用浏览器开发者工具
1. 打开F12开发者工具
2. 查看Console标签页
3. 观察WebSocket连接日志

#### 使用在线工具
访问 `http://your-domain.com/websocket-test.html` 进行测试

#### 使用命令行工具
```bash
# 测试WebSocket连接
wscat -c ws://your-domain.com:8080

# 或使用curl测试HTTP升级
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
     http://your-domain.com:8080/
```

## 故障排除

### 1. 连接被拒绝
- 检查WebSocket服务是否启动：`ps aux | grep webman`
- 检查端口是否监听：`netstat -tlnp | grep 8080`
- 检查防火墙设置

### 2. 连接超时
- 检查网络连通性：`telnet your-domain.com 8080`
- 检查云服务器安全组配置
- 检查代理服务器配置

### 3. SSL/TLS错误
- 确保HTTPS站点使用WSS协议
- 检查SSL证书是否包含WebSocket域名
- 考虑使用Nginx代理WebSocket

### 4. 跨域问题
WebSocket服务器已配置允许跨域连接，如果仍有问题，检查：
- 浏览器安全策略
- 代理服务器CORS配置

## 监控和日志

### 查看WebSocket日志
```bash
# 查看webman日志
tail -f runtime/logs/workerman.log

# 查看应用日志
tail -f runtime/logs/webman.log
```

### 监控连接状态
可以在WebSocket服务器中添加连接统计：
```php
// 在WebSocketServer.php中添加
public static function getConnectionCount() {
    return count(self::$connections);
}
```

## 性能优化

### 1. 连接池管理
- 限制最大连接数
- 实现连接超时清理
- 添加心跳检测

### 2. 负载均衡
对于高并发场景，可以考虑：
- 多个WebSocket服务器实例
- Redis集群共享状态
- Nginx负载均衡

### 3. 资源监控
- 监控内存使用
- 监控连接数量
- 监控消息处理延迟
